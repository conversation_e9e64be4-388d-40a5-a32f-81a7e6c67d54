/**
 * @fileoverview Commitizen 适配器
 * @description 为 Commitizen 提供自定义的提交消息生成器，支持 Applies-To 和 Issue-ID 字段
 */

import { COMMIT_TYPES, REGEX_PATTERNS } from './scripts/changelog-release/constants.ts';
import { listExtensions } from './scripts/extension-config/utils.ts';

/**
 * 生成 Commitizen 的提交类型选项
 */
function generateCommitTypeChoices() {
  return Object.entries(COMMIT_TYPES).map(([type, config]) => {
    return {
      value: type,
      name: `${type}:${' '.repeat(Math.max(1, 10 - type.length))}${config.description}`,
      short: config.description,
    };
  });
}

/**
 * 获取可用的扩展插件列表
 */
async function getAvailableExtensions() {
  try {
    return listExtensions();
  } catch (error) {
    console.warn('无法获取扩展列表，使用默认选项:', error.message);
    return ['cookies_manager', 'price_tracker', 'extension_dashboard'];
  }
}

/**
 * Commitizen 适配器配置
 */
export default {
  prompter(cz, commit) {
    console.log('\n🎯 欢迎使用 Changelog & Release 提交向导!\n');
    console.log('📋 请按照提示填写提交信息，确保符合项目规范。\n');

    cz.prompt([
      {
        type: 'list',
        name: 'type',
        message: '选择提交类型:',
        choices: generateCommitTypeChoices(),
        pageSize: 12,
      },
      {
        type: 'input',
        name: 'scope',
        message: '影响范围 (可选，如: auth, ui, api):',
        filter: (value) => value.trim(),
      },
      {
        type: 'input',
        name: 'subject',
        message: '简短描述 (必填):',
        validate: (value) => {
          if (value.trim().length === 0) {
            return '提交描述不能为空';
          }
          if (value.trim().length > 72) {
            return '提交描述不能超过 72 个字符';
          }
          return true;
        },
        filter: (value) => value.trim(),
      },
      {
        type: 'input',
        name: 'body',
        message: '详细描述 (可选，支持多行):',
        filter: (value) => value.trim(),
      },
      {
        type: 'checkbox',
        name: 'appliesTo',
        message: '✅ 应用到哪些扩展 (必填):',
        choices: async () => {
          const extensions = await getAvailableExtensions();
          return extensions.map(ext => ({
            name: ext,
            value: ext,
            checked: false,
          }));
        },
        validate: (choices) => {
          if (choices.length === 0) {
            return '必须选择至少一个扩展插件';
          }
          return true;
        },
        pageSize: 10,
      },
      {
        type: 'input',
        name: 'issueId',
        message: '🔗 关联的 Issue ID (可选，格式: 1#20250712-01):',
        validate: (value) => {
          if (value.trim() === '') {
            return true; // 可选字段
          }
          // 支持两种格式: 1#20250712-01 或 20250712-01
          if (!REGEX_PATTERNS.issueId.test(value.trim())) {
            return 'Issue ID 格式不正确，应为: 数字#日期-序号 (如: 1#20250712-01) 或 日期-序号 (如: #20250712-01)';
          }
          return true;
        },
        filter: (value) => value.trim(),
      },
      {
        type: 'confirm',
        name: 'isBreaking',
        message: '💥 是否为重大变更 (Breaking Change)?',
        default: false,
      },
      {
        type: 'input',
        name: 'breakingChange',
        message: '📝 描述重大变更的影响 (必填):',
        when: (answers) => answers.isBreaking,
        validate: (value) => {
          if (value.trim().length === 0) {
            return '重大变更必须提供详细描述';
          }
          return true;
        },
        filter: (value) => value.trim(),
      },
    ]).then((answers) => {
      const {
        type,
        scope,
        subject,
        body,
        appliesTo,
        issueId,
        isBreaking,
        breakingChange,
      } = answers;

      // 构建提交消息头部
      const scopeStr = scope ? `(${scope})` : '';
      const breakingStr = isBreaking ? '!' : '';
      const header = `${type}${scopeStr}${breakingStr}: ${subject}`;

      // 构建提交消息正文
      const bodyParts = [];
      
      if (body) {
        bodyParts.push(body);
        bodyParts.push(''); // 空行分隔
      }

      if (isBreaking && breakingChange) {
        bodyParts.push(`**重大变更**: ${breakingChange}`);
        bodyParts.push(''); // 空行分隔
      }

      // 添加元数据
      bodyParts.push(`Applies-To: ${appliesTo.join(', ')}`);
      
      if (issueId) {
        bodyParts.push(`Issue-ID: ${issueId}`);
      }

      // 组装完整的提交消息
      const fullMessage = bodyParts.length > 0 
        ? `${header}\n\n${bodyParts.join('\n')}`
        : header;

      // 显示预览
      console.log('\n📋 提交消息预览:');
      console.log('─'.repeat(50));
      console.log(fullMessage);
      console.log('─'.repeat(50));

      commit(fullMessage);
    });
  },
};

/**
 * 验证 Commitizen 配置
 */
export function validateCommitizenConfig() {
  const errors = [];

  // 检查提交类型配置
  if (!COMMIT_TYPES || Object.keys(COMMIT_TYPES).length === 0) {
    errors.push('COMMIT_TYPES 未定义或为空');
  }

  // 检查必需的提交类型
  const requiredTypes = ['feat', 'fix', 'docs', 'style', 'refactor', 'test', 'chore'];
  for (const type of requiredTypes) {
    if (!COMMIT_TYPES[type]) {
      errors.push(`缺少必需的提交类型: ${type}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 获取提交消息模板
 */
export function getCommitMessageTemplate() {
  return `<type>(<scope>): <subject>

[optional body]

Applies-To: <extension-name-1>, <extension-name-2>
Issue-ID: <issue-id>

# 提交类型说明:
${Object.entries(COMMIT_TYPES).map(([type, config]) => {
  return `# ${type}: ${config.description}`;
}).join('\n')}

# 格式要求:
# - subject: 简短描述，不超过 72 字符
# - scope: 影响范围 (可选)
# - Applies-To: 必填，应用的扩展名称
# - Issue-ID: 可选，格式为 数字#日期-序号 或 日期-序号
# - 重大变更: 在 type 后添加 ! 或在正文中描述

# 示例:
# feat(auth): 添加OAuth登录支持
# 
# 新增Google和GitHub OAuth登录功能，提升用户体验。
# 
# Applies-To: extension_dashboard
# Issue-ID: 1#20250712-01
`;
}
